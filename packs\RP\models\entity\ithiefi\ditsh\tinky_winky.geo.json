{"format_version": "1.10.0", "geometry.ithiefi_ditsh_tinky_winky": {"texturewidth": 64, "textureheight": 32, "visible_bounds_width": 3, "visible_bounds_height": 3, "visible_bounds_offset": [0, 1.5, 0], "bones": [{"name": "waist", "pivot": [0, 12, 0]}, {"name": "body", "parent": "waist", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 12, -2], "size": [8, 12, 4], "uv": [16, 16]}]}, {"name": "head", "parent": "body", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 24, -4], "size": [8, 8, 8], "uv": [0, 0]}]}, {"name": "hat", "parent": "head", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 24, -4], "size": [8, 8, 8], "uv": [32, 0], "inflate": 0.5}]}, {"name": "rightArm", "parent": "body", "pivot": [-5, 22, 0], "rotation": [0, 0, 25], "cubes": [{"origin": [-8, 12, -2], "size": [4, 12, 4], "uv": [40, 16]}]}, {"name": "rightItem", "parent": "rightArm", "pivot": [-6, 15, 1]}, {"name": "leftArm", "parent": "body", "pivot": [5, 22, 0], "rotation": [0, 0, -22.5], "mirror": true, "cubes": [{"origin": [4, 12, -2], "size": [4, 12, 4], "uv": [40, 16]}]}, {"name": "rightLeg", "parent": "body", "pivot": [-1.9, 12, 0], "cubes": [{"origin": [-3.9, 0, -2], "size": [4, 12, 4], "uv": [0, 16]}]}, {"name": "leftLeg", "parent": "body", "pivot": [1.9, 12, 0], "mirror": true, "cubes": [{"origin": [-0.1, 0, -2], "size": [4, 12, 4], "uv": [0, 16]}]}]}}